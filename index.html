<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi Email Sender</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .upload-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .upload-section h2 {
            margin-bottom: 1rem;
            color: #333;
        }

        .file-upload {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            transition: border-color 0.3s ease;
        }

        .file-upload:hover {
            border-color: #667eea;
        }

        .file-upload.dragover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            transition: transform 0.2s ease;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
        }

        .upload-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .file-info {
            margin-top: 1rem;
            padding: 1rem;
            background-color: #f8f9fa;
            border-radius: 5px;
            display: none;
        }

        .controls {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background-color: #007bff;
            color: white;
        }

        .btn-danger {
            background-color: #dc3545;
            color: white;
        }

        .btn:hover {
            transform: translateY(-1px);
            opacity: 0.9;
        }

        .contacts-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .contacts-header {
            background-color: #f8f9fa;
            padding: 1.5rem;
            border-bottom: 1px solid #dee2e6;
        }

        .contacts-header h2 {
            margin: 0;
            color: #333;
        }

        .contacts-count {
            color: #666;
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }

        .table-container {
            overflow-x: auto;
            max-height: 600px;
            overflow-y: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }

        th {
            background-color: #f8f9fa;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        tr:hover {
            background-color: #f8f9ff;
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #666;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }

        .alert-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .alert-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .alert-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .table-container {
                font-size: 0.9rem;
            }

            th, td {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Multi Email Sender</h1>
            <p>Import contacts from Excel and manage your email campaigns</p>
        </div>

        <div id="alerts"></div>

        <div class="upload-section">
            <h2>Upload Excel File</h2>
            <div class="file-upload" id="fileUpload">
                <p>Drag and drop your Excel file here or click to browse</p>
                <p style="font-size: 0.9rem; color: #666; margin-top: 0.5rem;">
                    Supported formats: .xlsx, .xls (Max size: 10MB)<br>
                    Expected columns: Principal Name, Email, GDEemail, Cell Phone
                </p>
                <input type="file" id="fileInput" class="file-input" accept=".xlsx,.xls">
                <button type="button" class="upload-btn" onclick="document.getElementById('fileInput').click()">
                    Choose File
                </button>
            </div>
            <div class="file-info" id="fileInfo"></div>
        </div>

        <div class="controls">
            <button class="btn btn-primary" onclick="loadContactSets()">Refresh Data</button>
            <button class="btn btn-danger" onclick="clearAllContacts()">Clear All Data</button>
            <span id="contactsCount" class="contacts-count"></span>
        </div>

        <!-- Contact Sets Section -->
        <div class="contacts-section" style="margin-bottom: 2rem;">
            <div class="contacts-header">
                <h2>Contact Sets</h2>
                <div class="contacts-count" id="contactSetsCountHeader">No contact sets loaded</div>
            </div>
            <div class="table-container">
                <div id="contactSetsTable">
                    <div class="empty-state" id="contactSetsEmptyState">
                        <div style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;">📁</div>
                        <h3>No contact sets yet</h3>
                        <p>Upload an Excel file to create your first contact set</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Groups Section -->
        <div class="contacts-section" id="groupsSection" style="margin-bottom: 2rem; display: none;">
            <div class="contacts-header">
                <h2>Groups</h2>
                <div class="contacts-count" id="groupsCountHeader">No groups created</div>
                <div style="margin-top: 1rem;">
                    <input type="number" id="groupSizeInput" placeholder="Group size (e.g., 50)" min="1" style="padding: 8px; margin-right: 10px; border: 1px solid #ddd; border-radius: 4px;">
                    <button class="btn btn-primary" onclick="createGroups()">Create Groups</button>
                </div>
            </div>
            <div class="table-container">
                <div id="groupsTable">
                    <div class="empty-state" id="groupsEmptyState">
                        <div style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;">👥</div>
                        <h3>No groups created</h3>
                        <p>Select a contact set and specify group size to create groups</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="contacts-section">
            <div class="contacts-header">
                <h2>Imported Contacts</h2>
                <div class="contacts-count" id="contactsCountHeader">No contacts loaded</div>
            </div>
            <div class="table-container">
                <div id="contactsTable">
                    <div class="loading" id="loadingState" style="display: none;">
                        <div class="spinner"></div>
                        <p>Loading contacts...</p>
                    </div>
                    <div class="empty-state" id="emptyState">
                        <div style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;">📊</div>
                        <h3>No contacts yet</h3>
                        <p>Upload an Excel file to get started</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let contacts = [];
        let contactSets = [];
        let selectedContactSetId = null;
        let groups = [];

        // API base URL - change this if your backend runs on a different port
        const API_BASE_URL = window.location.hostname === 'localhost' && window.location.port === '3000'
            ? '' // Use relative URLs when served from Express
            : 'http://localhost:3000'; // Use absolute URL when served from different server

        // DOM elements
        const fileInput = document.getElementById('fileInput');
        const fileUpload = document.getElementById('fileUpload');
        const fileInfo = document.getElementById('fileInfo');
        const alertsContainer = document.getElementById('alerts');
        const contactsTable = document.getElementById('contactsTable');
        const loadingState = document.getElementById('loadingState');
        const emptyState = document.getElementById('emptyState');
        const contactsCount = document.getElementById('contactsCount');
        const contactsCountHeader = document.getElementById('contactsCountHeader');

        // New DOM elements for groups functionality
        const contactSetsTable = document.getElementById('contactSetsTable');
        const contactSetsEmptyState = document.getElementById('contactSetsEmptyState');
        const contactSetsCountHeader = document.getElementById('contactSetsCountHeader');
        const groupsSection = document.getElementById('groupsSection');
        const groupsTable = document.getElementById('groupsTable');
        const groupsEmptyState = document.getElementById('groupsEmptyState');
        const groupsCountHeader = document.getElementById('groupsCountHeader');
        const groupSizeInput = document.getElementById('groupSizeInput');

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            loadContactSets();
        });

        function setupEventListeners() {
            // File input change
            fileInput.addEventListener('change', handleFileSelect);

            // Drag and drop
            fileUpload.addEventListener('dragover', handleDragOver);
            fileUpload.addEventListener('dragleave', handleDragLeave);
            fileUpload.addEventListener('drop', handleFileDrop);
        }

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                displayFileInfo(file);
                uploadFile(file);
            }
        }

        function handleDragOver(event) {
            event.preventDefault();
            fileUpload.classList.add('dragover');
        }

        function handleDragLeave(event) {
            event.preventDefault();
            fileUpload.classList.remove('dragover');
        }

        function handleFileDrop(event) {
            event.preventDefault();
            fileUpload.classList.remove('dragover');

            const files = event.dataTransfer.files;
            if (files.length > 0) {
                const file = files[0];
                fileInput.files = files;
                displayFileInfo(file);
                uploadFile(file);
            }
        }

        function displayFileInfo(file) {
            const fileSize = (file.size / 1024 / 1024).toFixed(2);
            fileInfo.innerHTML = `
                <strong>Selected file:</strong> ${file.name}<br>
                <strong>Size:</strong> ${fileSize} MB<br>
                <strong>Type:</strong> ${file.type}
            `;
            fileInfo.style.display = 'block';
        }

        async function uploadFile(file) {
            const formData = new FormData();
            formData.append('excelFile', file);

            try {
                showAlert('Uploading and processing file...', 'info');

                const response = await fetch(`${API_BASE_URL}/api/upload-excel`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (response.ok) {
                    showAlert(
                        `Success! Imported ${result.insertedCount} contacts from ${result.totalRows} rows into "${result.contactSetName}".`,
                        'success'
                    );
                    loadContactSets();

                    // Clear file input
                    fileInput.value = '';
                    fileInfo.style.display = 'none';
                } else {
                    showAlert(`Error: ${result.error}`, 'error');
                }
            } catch (error) {
                console.error('Upload error:', error);
                showAlert('Failed to upload file. Please try again.', 'error');
            }
        }

        async function loadContactSets() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/contact-sets`);
                const data = await response.json();

                if (response.ok) {
                    contactSets = data;
                    displayContactSets(contactSets);
                    updateContactSetsCount(contactSets.length);

                    // Also load all contacts for the main table
                    loadContacts();
                } else {
                    showAlert(`Error loading contact sets: ${data.error}`, 'error');
                }
            } catch (error) {
                console.error('Load contact sets error:', error);
                showAlert('Failed to load contact sets. Please try again.', 'error');
            }
        }

        async function loadContacts() {
            try {
                showLoading(true);

                const response = await fetch(`${API_BASE_URL}/api/contacts`);
                const data = await response.json();

                if (response.ok) {
                    contacts = data;
                    displayContacts(contacts);
                    updateContactsCount(contacts.length);
                } else {
                    showAlert(`Error loading contacts: ${data.error}`, 'error');
                }
            } catch (error) {
                console.error('Load contacts error:', error);
                showAlert('Failed to load contacts. Please try again.', 'error');
            } finally {
                showLoading(false);
            }
        }

        async function loadContactsBySet(contactSetId) {
            try {
                showLoading(true);

                const response = await fetch(`${API_BASE_URL}/api/contact-sets/${contactSetId}/contacts`);
                const data = await response.json();

                if (response.ok) {
                    contacts = data;
                    displayContacts(contacts);
                    updateContactsCount(contacts.length);
                } else {
                    showAlert(`Error loading contacts: ${data.error}`, 'error');
                }
            } catch (error) {
                console.error('Load contacts by set error:', error);
                showAlert('Failed to load contacts. Please try again.', 'error');
            } finally {
                showLoading(false);
            }
        }

        async function loadGroups(contactSetId) {
            try {
                const response = await fetch(`${API_BASE_URL}/api/contact-sets/${contactSetId}/groups`);
                const data = await response.json();

                if (response.ok) {
                    groups = data;
                    displayGroups(groups);
                    updateGroupsCount(groups.length);
                    groupsSection.style.display = 'block';
                } else {
                    showAlert(`Error loading groups: ${data.error}`, 'error');
                }
            } catch (error) {
                console.error('Load groups error:', error);
                showAlert('Failed to load groups. Please try again.', 'error');
            }
        }

        async function loadContactsByGroup(groupId) {
            try {
                showLoading(true);

                const response = await fetch(`${API_BASE_URL}/api/groups/${groupId}/contacts`);
                const data = await response.json();

                if (response.ok) {
                    contacts = data;
                    displayContacts(contacts);
                    updateContactsCount(contacts.length);
                } else {
                    showAlert(`Error loading contacts by group: ${data.error}`, 'error');
                }
            } catch (error) {
                console.error('Load contacts by group error:', error);
                showAlert('Failed to load contacts by group. Please try again.', 'error');
            } finally {
                showLoading(false);
            }
        }

        function displayContactSets(contactSetsData) {
            if (contactSetsData.length === 0) {
                contactSetsTable.innerHTML = '';
                contactSetsTable.appendChild(contactSetsEmptyState);
                return;
            }

            const table = document.createElement('table');
            table.innerHTML = `
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Filename</th>
                        <th>Total Contacts</th>
                        <th>Groups</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    ${contactSetsData.map(set => `
                        <tr>
                            <td>${set.name}</td>
                            <td>${set.filename || '-'}</td>
                            <td>${set.total_contacts}</td>
                            <td>${set.group_count}</td>
                            <td>${new Date(set.created_at).toLocaleDateString()}</td>
                            <td>
                                <button class="btn btn-primary" style="font-size: 0.8rem; padding: 5px 10px;" onclick="selectContactSet(${set.id})">View</button>
                                ${set.group_count === 0 ?
                                    `<button class="btn btn-primary" style="font-size: 0.8rem; padding: 5px 10px; margin-left: 5px;" onclick="showGroupCreation(${set.id})">Create Groups</button>` :
                                    `<button class="btn btn-primary" style="font-size: 0.8rem; padding: 5px 10px; margin-left: 5px;" onclick="viewGroups(${set.id})">View Groups</button>`
                                }
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            `;

            contactSetsTable.innerHTML = '';
            contactSetsTable.appendChild(table);
        }

        function displayGroups(groupsData) {
            if (groupsData.length === 0) {
                groupsTable.innerHTML = '';
                groupsTable.appendChild(groupsEmptyState);
                return;
            }

            const table = document.createElement('table');
            table.innerHTML = `
                <thead>
                    <tr>
                        <th>Group Number</th>
                        <th>Group Name</th>
                        <th>Expected Size</th>
                        <th>Actual Contacts</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    ${groupsData.map(group => `
                        <tr style="cursor: pointer;" onclick="viewGroupContacts(${group.id}, '${group.name}')">
                            <td>${group.group_number}</td>
                            <td>${group.name}</td>
                            <td>${group.size}</td>
                            <td>${group.actual_contact_count}</td>
                            <td>${new Date(group.created_at).toLocaleDateString()}</td>
                            <td>
                                <button class="btn btn-primary" style="font-size: 0.8rem; padding: 5px 10px;" onclick="event.stopPropagation(); viewGroupContacts(${group.id}, '${group.name}')">View Contacts</button>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            `;

            groupsTable.innerHTML = '';
            groupsTable.appendChild(table);
        }

        function displayContacts(contactsData) {
            if (contactsData.length === 0) {
                contactsTable.innerHTML = '';
                contactsTable.appendChild(emptyState);
                return;
            }

            const table = document.createElement('table');
            table.innerHTML = `
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Principal Name</th>
                        <th>Email</th>
                        <th>GDE Email</th>
                        <th>Cell Phone</th>
                        <th>Group</th>
                        <th>Created</th>
                    </tr>
                </thead>
                <tbody>
                    ${contactsData.map(contact => `
                        <tr>
                            <td>${contact.id}</td>
                            <td>${contact.principal_name || '-'}</td>
                            <td>${contact.email || '-'}</td>
                            <td>${contact.gde_email || '-'}</td>
                            <td>${contact.cell_phone || '-'}</td>
                            <td>${contact.group_name || '-'}</td>
                            <td>${new Date(contact.created_at).toLocaleDateString()}</td>
                        </tr>
                    `).join('')}
                </tbody>
            `;

            contactsTable.innerHTML = '';
            contactsTable.appendChild(table);
        }

        // Group management functions
        function selectContactSet(contactSetId) {
            selectedContactSetId = contactSetId;
            loadContactsBySet(contactSetId);

            // Find the contact set name
            const contactSet = contactSets.find(set => set.id === contactSetId);
            if (contactSet) {
                showAlert(`Viewing contacts from "${contactSet.name}"`, 'info');
            }
        }

        function showGroupCreation(contactSetId) {
            selectedContactSetId = contactSetId;
            groupsSection.style.display = 'block';
            groupSizeInput.focus();

            // Find the contact set
            const contactSet = contactSets.find(set => set.id === contactSetId);
            if (contactSet) {
                showAlert(`Create groups for "${contactSet.name}" (${contactSet.total_contacts} contacts)`, 'info');
            }
        }

        function viewGroups(contactSetId) {
            selectedContactSetId = contactSetId;
            loadGroups(contactSetId);
        }

        function viewGroupContacts(groupId, groupName) {
            loadContactsByGroup(groupId);
            showAlert(`Viewing contacts from "${groupName}"`, 'info');

            // Scroll to contacts section
            document.querySelector('.contacts-section:last-child').scrollIntoView({
                behavior: 'smooth'
            });
        }

        async function createGroups() {
            if (!selectedContactSetId) {
                showAlert('Please select a contact set first', 'error');
                return;
            }

            const groupSize = parseInt(groupSizeInput.value);
            if (!groupSize || groupSize < 1) {
                showAlert('Please enter a valid group size', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/api/contact-sets/${selectedContactSetId}/create-groups`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ groupSize })
                });

                const result = await response.json();

                if (response.ok) {
                    showAlert(`Successfully created ${result.groupCount} groups!`, 'success');
                    loadGroups(selectedContactSetId);
                    loadContactSets(); // Refresh contact sets to update group count
                    groupSizeInput.value = '';
                } else {
                    showAlert(`Error: ${result.error}`, 'error');
                }
            } catch (error) {
                console.error('Create groups error:', error);
                showAlert('Failed to create groups. Please try again.', 'error');
            }
        }

        async function clearAllContacts() {
            if (!confirm('Are you sure you want to delete all data (contacts, groups, and contact sets)? This action cannot be undone.')) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/api/contacts`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (response.ok) {
                    showAlert(`Successfully deleted ${result.deletedContacts} contacts, ${result.deletedGroups} groups, and ${result.deletedSets} contact sets.`, 'success');
                    loadContactSets();
                    groupsSection.style.display = 'none';
                    selectedContactSetId = null;
                } else {
                    showAlert(`Error: ${result.error}`, 'error');
                }
            } catch (error) {
                console.error('Clear contacts error:', error);
                showAlert('Failed to clear data. Please try again.', 'error');
            }
        }

        function updateContactsCount(count) {
            const countText = count === 0 ? 'No contacts' : `${count} contact${count === 1 ? '' : 's'}`;
            contactsCount.textContent = countText;
            contactsCountHeader.textContent = countText;
        }

        function updateContactSetsCount(count) {
            const countText = count === 0 ? 'No contact sets' : `${count} contact set${count === 1 ? '' : 's'}`;
            contactSetsCountHeader.textContent = countText;
        }

        function updateGroupsCount(count) {
            const countText = count === 0 ? 'No groups' : `${count} group${count === 1 ? '' : 's'}`;
            groupsCountHeader.textContent = countText;
        }

        function showLoading(show) {
            if (show) {
                contactsTable.innerHTML = '';
                contactsTable.appendChild(loadingState);
                loadingState.style.display = 'block';
            } else {
                loadingState.style.display = 'none';
            }
        }

        function showAlert(message, type) {
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;

            alertsContainer.appendChild(alert);

            // Auto-remove alert after 5 seconds
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 5000);
        }
    </script>
</body>
</html>