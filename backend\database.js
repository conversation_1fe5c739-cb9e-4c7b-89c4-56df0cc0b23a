const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Create database connection
const dbPath = path.join(__dirname, 'contacts.db');
const db = new sqlite3.Database(dbPath);

// Initialize database with contacts table
function initializeDatabase() {
    return new Promise((resolve, reject) => {
        db.serialize(() => {
            // Create contacts table if it doesn't exist
            db.run(`
                CREATE TABLE IF NOT EXISTS contacts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    principal_name TEXT,
                    email TEXT,
                    gde_email TEXT,
                    cell_phone TEXT,
                    notes TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            `, (err) => {
                if (err) {
                    console.error('Error creating contacts table:', err);
                    reject(err);
                } else {
                    console.log('Database initialized successfully');
                    resolve();
                }
            });
        });
    });
}

// Insert multiple contacts
function insertContacts(contacts) {
    return new Promise((resolve, reject) => {
        const stmt = db.prepare(`
            INSERT INTO contacts (principal_name, email, gde_email, cell_phone, notes)
            VALUES (?, ?, ?, ?, ?)
        `);

        let insertedCount = 0;
        let errors = [];

        contacts.forEach((contact, index) => {
            stmt.run([
                contact.principal_name || '',
                contact.email || '',
                contact.gde_email || '',
                contact.cell_phone || '',
                contact.notes || ''
            ], function(err) {
                if (err) {
                    errors.push({ index, error: err.message });
                } else {
                    insertedCount++;
                }

                // Check if this is the last contact
                if (index === contacts.length - 1) {
                    stmt.finalize();
                    if (errors.length > 0) {
                        reject({ insertedCount, errors });
                    } else {
                        resolve({ insertedCount });
                    }
                }
            });
        });
    });
}

// Get all contacts
function getAllContacts() {
    return new Promise((resolve, reject) => {
        db.all('SELECT * FROM contacts ORDER BY created_at DESC', (err, rows) => {
            if (err) {
                reject(err);
            } else {
                resolve(rows);
            }
        });
    });
}

// Delete all contacts
function clearAllContacts() {
    return new Promise((resolve, reject) => {
        db.run('DELETE FROM contacts', function(err) {
            if (err) {
                reject(err);
            } else {
                resolve({ deletedCount: this.changes });
            }
        });
    });
}

// Close database connection
function closeDatabase() {
    return new Promise((resolve) => {
        db.close((err) => {
            if (err) {
                console.error('Error closing database:', err);
            } else {
                console.log('Database connection closed');
            }
            resolve();
        });
    });
}

module.exports = {
    initializeDatabase,
    insertContacts,
    getAllContacts,
    clearAllContacts,
    closeDatabase
};
