const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Create database connection
const dbPath = path.join(__dirname, 'contacts.db');
const db = new sqlite3.Database(dbPath);

// Initialize database with contacts table
function initializeDatabase() {
    return new Promise((resolve, reject) => {
        db.serialize(() => {
            // Create contact_sets table
            db.run(`
                CREATE TABLE IF NOT EXISTS contact_sets (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    filename TEXT,
                    total_contacts INTEGER DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            `, (err) => {
                if (err) {
                    console.error('Error creating contact_sets table:', err);
                    reject(err);
                    return;
                }

                // Create groups table
                db.run(`
                    CREATE TABLE IF NOT EXISTS groups (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        contact_set_id INTEGER NOT NULL,
                        name TEXT NOT NULL,
                        group_number INTEGER NOT NULL,
                        size INTEGER NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (contact_set_id) REFERENCES contact_sets (id) ON DELETE CASCADE
                    )
                `, (err) => {
                    if (err) {
                        console.error('Error creating groups table:', err);
                        reject(err);
                        return;
                    }

                    // Create contacts table if it doesn't exist
                    db.run(`
                        CREATE TABLE IF NOT EXISTS contacts (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            contact_set_id INTEGER NOT NULL,
                            group_id INTEGER,
                            principal_name TEXT,
                            email TEXT,
                            gde_email TEXT,
                            cell_phone TEXT,
                            notes TEXT,
                            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                            FOREIGN KEY (contact_set_id) REFERENCES contact_sets (id) ON DELETE CASCADE,
                            FOREIGN KEY (group_id) REFERENCES groups (id) ON DELETE SET NULL
                        )
                    `, (err) => {
                        if (err) {
                            console.error('Error creating contacts table:', err);
                            reject(err);
                        } else {
                            console.log('Database initialized successfully');
                            resolve();
                        }
                    });
                });
            });
        });
    });
}

// Create a new contact set
function createContactSet(name, filename) {
    return new Promise((resolve, reject) => {
        const stmt = db.prepare(`
            INSERT INTO contact_sets (name, filename)
            VALUES (?, ?)
        `);

        stmt.run([name, filename], function(err) {
            stmt.finalize();
            if (err) {
                reject(err);
            } else {
                resolve({ id: this.lastID, name, filename });
            }
        });
    });
}

// Insert multiple contacts with contact set ID
function insertContacts(contacts, contactSetId) {
    return new Promise((resolve, reject) => {
        const stmt = db.prepare(`
            INSERT INTO contacts (contact_set_id, principal_name, email, gde_email, cell_phone, notes)
            VALUES (?, ?, ?, ?, ?, ?)
        `);

        let insertedCount = 0;
        let errors = [];

        contacts.forEach((contact, index) => {
            stmt.run([
                contactSetId,
                contact.principal_name || '',
                contact.email || '',
                contact.gde_email || '',
                contact.cell_phone || '',
                contact.notes || ''
            ], function(err) {
                if (err) {
                    errors.push({ index, error: err.message });
                } else {
                    insertedCount++;
                }

                // Check if this is the last contact
                if (index === contacts.length - 1) {
                    stmt.finalize();
                    if (errors.length > 0) {
                        reject({ insertedCount, errors });
                    } else {
                        // Update total_contacts in contact_sets
                        db.run(`UPDATE contact_sets SET total_contacts = ? WHERE id = ?`,
                               [insertedCount, contactSetId], (updateErr) => {
                            if (updateErr) {
                                console.error('Error updating contact set total:', updateErr);
                            }
                            resolve({ insertedCount, contactSetId });
                        });
                    }
                }
            });
        });
    });
}

// Create groups for a contact set
function createGroups(contactSetId, groupSize) {
    return new Promise((resolve, reject) => {
        // First, get the total number of contacts in this set
        db.get('SELECT total_contacts FROM contact_sets WHERE id = ?', [contactSetId], (err, row) => {
            if (err) {
                reject(err);
                return;
            }

            const totalContacts = row.total_contacts;
            const numberOfGroups = Math.ceil(totalContacts / groupSize);

            // Create groups
            const stmt = db.prepare(`
                INSERT INTO groups (contact_set_id, name, group_number, size)
                VALUES (?, ?, ?, ?)
            `);

            let createdGroups = [];
            let completed = 0;

            for (let i = 1; i <= numberOfGroups; i++) {
                const actualSize = i === numberOfGroups ?
                    totalContacts - ((numberOfGroups - 1) * groupSize) : groupSize;

                stmt.run([contactSetId, `Group ${i}`, i, actualSize], function(err) {
                    if (err) {
                        stmt.finalize();
                        reject(err);
                        return;
                    }

                    createdGroups.push({
                        id: this.lastID,
                        name: `Group ${i}`,
                        group_number: i,
                        size: actualSize
                    });

                    completed++;
                    if (completed === numberOfGroups) {
                        stmt.finalize();
                        resolve(createdGroups);
                    }
                });
            }
        });
    });
}

// Assign contacts to groups
function assignContactsToGroups(contactSetId, groupSize) {
    return new Promise((resolve, reject) => {
        // Get all contacts for this set ordered by ID
        db.all('SELECT id FROM contacts WHERE contact_set_id = ? ORDER BY id', [contactSetId], (err, contacts) => {
            if (err) {
                reject(err);
                return;
            }

            // Get all groups for this set
            db.all('SELECT id, group_number FROM groups WHERE contact_set_id = ? ORDER BY group_number',
                   [contactSetId], (err, groups) => {
                if (err) {
                    reject(err);
                    return;
                }

                const stmt = db.prepare('UPDATE contacts SET group_id = ? WHERE id = ?');
                let updated = 0;

                contacts.forEach((contact, index) => {
                    const groupIndex = Math.floor(index / groupSize);
                    const group = groups[groupIndex];

                    stmt.run([group.id, contact.id], (err) => {
                        if (err) {
                            console.error('Error assigning contact to group:', err);
                        }

                        updated++;
                        if (updated === contacts.length) {
                            stmt.finalize();
                            resolve({ assignedContacts: updated });
                        }
                    });
                });
            });
        });
    });
}

// Get all contact sets
function getAllContactSets() {
    return new Promise((resolve, reject) => {
        db.all(`
            SELECT cs.*,
                   COUNT(g.id) as group_count
            FROM contact_sets cs
            LEFT JOIN groups g ON cs.id = g.contact_set_id
            GROUP BY cs.id
            ORDER BY cs.created_at DESC
        `, (err, rows) => {
            if (err) {
                reject(err);
            } else {
                resolve(rows);
            }
        });
    });
}

// Get all contacts
function getAllContacts() {
    return new Promise((resolve, reject) => {
        db.all(`
            SELECT c.*, cs.name as contact_set_name, g.name as group_name, g.group_number
            FROM contacts c
            LEFT JOIN contact_sets cs ON c.contact_set_id = cs.id
            LEFT JOIN groups g ON c.group_id = g.id
            ORDER BY c.created_at DESC
        `, (err, rows) => {
            if (err) {
                reject(err);
            } else {
                resolve(rows);
            }
        });
    });
}

// Get contacts by contact set
function getContactsBySet(contactSetId) {
    return new Promise((resolve, reject) => {
        db.all(`
            SELECT c.*, g.name as group_name, g.group_number
            FROM contacts c
            LEFT JOIN groups g ON c.group_id = g.id
            WHERE c.contact_set_id = ?
            ORDER BY g.group_number, c.id
        `, [contactSetId], (err, rows) => {
            if (err) {
                reject(err);
            } else {
                resolve(rows);
            }
        });
    });
}

// Get groups by contact set
function getGroupsBySet(contactSetId) {
    return new Promise((resolve, reject) => {
        db.all(`
            SELECT g.*, COUNT(c.id) as actual_contact_count
            FROM groups g
            LEFT JOIN contacts c ON g.id = c.group_id
            WHERE g.contact_set_id = ?
            GROUP BY g.id
            ORDER BY g.group_number
        `, [contactSetId], (err, rows) => {
            if (err) {
                reject(err);
            } else {
                resolve(rows);
            }
        });
    });
}

// Get contacts by group
function getContactsByGroup(groupId) {
    return new Promise((resolve, reject) => {
        db.all(`
            SELECT c.*, g.name as group_name, g.group_number, cs.name as contact_set_name
            FROM contacts c
            LEFT JOIN groups g ON c.group_id = g.id
            LEFT JOIN contact_sets cs ON c.contact_set_id = cs.id
            WHERE c.group_id = ?
            ORDER BY c.id
        `, [groupId], (err, rows) => {
            if (err) {
                reject(err);
            } else {
                resolve(rows);
            }
        });
    });
}

// Delete all contacts and related data
function clearAllContacts() {
    return new Promise((resolve, reject) => {
        db.serialize(() => {
            db.run('DELETE FROM contacts', function(contactErr) {
                if (contactErr) {
                    reject(contactErr);
                    return;
                }

                const deletedContacts = this.changes;

                db.run('DELETE FROM groups', function(groupErr) {
                    if (groupErr) {
                        reject(groupErr);
                        return;
                    }

                    db.run('DELETE FROM contact_sets', function(setErr) {
                        if (setErr) {
                            reject(setErr);
                        } else {
                            resolve({
                                deletedContacts,
                                deletedGroups: this.changes,
                                deletedSets: this.changes
                            });
                        }
                    });
                });
            });
        });
    });
}

// Close database connection
function closeDatabase() {
    return new Promise((resolve) => {
        db.close((err) => {
            if (err) {
                console.error('Error closing database:', err);
            } else {
                console.log('Database connection closed');
            }
            resolve();
        });
    });
}

module.exports = {
    initializeDatabase,
    createContactSet,
    insertContacts,
    createGroups,
    assignContactsToGroups,
    getAllContactSets,
    getAllContacts,
    getContactsBySet,
    getGroupsBySet,
    getContactsByGroup,
    clearAllContacts,
    closeDatabase
};
