const express = require('express');
const multer = require('multer');
const XLSX = require('xlsx');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const {
    initializeDatabase,
    insertContacts,
    getAllContacts,
    clearAllContacts
} = require('./database');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../')));

// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        const uploadDir = path.join(__dirname, 'uploads');
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        cb(null, Date.now() + '-' + file.originalname);
    }
});

const upload = multer({
    storage: storage,
    fileFilter: (req, file, cb) => {
        // Accept only Excel files
        const allowedTypes = [
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];
        if (allowedTypes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error('Only Excel files are allowed'), false);
        }
    },
    limits: {
        fileSize: 10 * 1024 * 1024 // 10MB limit
    }
});

// Helper function to parse Excel file
function parseExcelFile(filePath) {
    try {
        const workbook = XLSX.readFile(filePath);
        const sheetName = workbook.SheetNames[0]; // Use first sheet
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet);

        // Map Excel columns to our database fields
        const contacts = jsonData.map(row => {
            return {
                principal_name: row['Principal Name'] || row['principal name'] || row['PRINCIPAL NAME'] ||
                               row.PrincipalName || row.principalName || row.PRINCIPALNAME || '',
                email: row.Email || row.email || row.EMAIL || '',
                gde_email: row.GDEemail || row.gdeemail || row.GDEEMAIL ||
                          row['GDE email'] || row['gde email'] || row['GDE EMAIL'] || '',
                cell_phone: row['Cell Phone'] || row['cell phone'] || row['CELL PHONE'] ||
                           row.CellPhone || row.cellPhone || row.CELLPHONE ||
                           row.Phone || row.phone || row.PHONE || '',
                notes: row.Notes || row.notes || row.NOTES || ''
            };
        });

        return contacts;
    } catch (error) {
        throw new Error('Failed to parse Excel file: ' + error.message);
    }
}

// Routes

// Serve the main HTML file
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '../index.html'));
});

// Upload and process Excel file
app.post('/api/upload-excel', upload.single('excelFile'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: 'No file uploaded' });
        }

        const filePath = req.file.path;

        // Parse Excel file
        const contacts = parseExcelFile(filePath);

        if (contacts.length === 0) {
            return res.status(400).json({ error: 'No valid data found in Excel file' });
        }

        // Insert contacts into database
        const result = await insertContacts(contacts);

        // Clean up uploaded file
        fs.unlinkSync(filePath);

        res.json({
            message: 'Excel file processed successfully',
            insertedCount: result.insertedCount,
            totalRows: contacts.length
        });

    } catch (error) {
        console.error('Error processing Excel file:', error);

        // Clean up uploaded file if it exists
        if (req.file && fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
        }

        res.status(500).json({ error: error.message });
    }
});

// Get all contacts
app.get('/api/contacts', async (req, res) => {
    try {
        const contacts = await getAllContacts();
        res.json(contacts);
    } catch (error) {
        console.error('Error fetching contacts:', error);
        res.status(500).json({ error: 'Failed to fetch contacts' });
    }
});

// Clear all contacts
app.delete('/api/contacts', async (req, res) => {
    try {
        const result = await clearAllContacts();
        res.json({
            message: 'All contacts cleared successfully',
            deletedCount: result.deletedCount
        });
    } catch (error) {
        console.error('Error clearing contacts:', error);
        res.status(500).json({ error: 'Failed to clear contacts' });
    }
});

// Error handling middleware
app.use((error, req, res, next) => {
    if (error instanceof multer.MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({ error: 'File too large. Maximum size is 10MB.' });
        }
    }
    res.status(500).json({ error: error.message });
});

// Initialize database and start server
async function startServer() {
    try {
        await initializeDatabase();
        app.listen(PORT, () => {
            console.log(`Server running on http://localhost:${PORT}`);
        });
    } catch (error) {
        console.error('Failed to start server:', error);
        process.exit(1);
    }
}

startServer();